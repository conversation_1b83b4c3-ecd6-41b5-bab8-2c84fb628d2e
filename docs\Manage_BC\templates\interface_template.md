# {{interfaceName}} Interface

## 📋 Overview

**Namespace**: `{{namespace}}`  
**Source File**: `{{filePath}}`  
**Purpose**: {{purpose}}

{{description}}

## 🏗️ Interface Definition

```csharp
{{interfaceDefinition}}
```

## 📊 Properties

| Property | Type | Access | Constraints | Description |
|----------|------|--------|-------------|-------------|
{{#each properties}}
| `{{name}}` | {{type}} | {{access}} | {{constraints}} | {{description}} |
{{/each}}

## 🔧 Methods

{{#each methods}}
### {{name}}

**Signature**: `{{signature}}`  
**Purpose**: {{purpose}}

#### Parameters
{{#each parameters}}
- **{{name}}** (`{{type}}`): {{description}}
  {{#if constraints}}- Constraints: {{constraints}}{{/if}}
{{/each}}

#### Returns
- **Type**: `{{returnType}}`
- **Description**: {{returnDescription}}

#### Constraints
{{#each constraints}}
- {{constraint}}
{{/each}}

---
{{/each}}

## 🔗 Inheritance Hierarchy

### Base Interfaces
{{#each baseInterfaces}}
- `{{name}}` - {{description}}
  {{#if properties}}- Inherited Properties: {{properties}}{{/if}}
{{/each}}

### Derived Interfaces
{{#each derivedInterfaces}}
- `{{name}}` - {{description}}
{{/each}}

## 🎯 Generic Constraints

{{#each genericConstraints}}
### {{typeParameter}}
{{#each constraints}}
- `{{constraint}}` - {{description}}
{{/each}}
{{/each}}

## 🏛️ Implementations

### Core Implementations
{{#each coreImplementations}}
- **{{className}}** (`{{namespace}}`)
  - **Purpose**: {{purpose}}
  - **File**: `{{filePath}}`
{{/each}}

### System-Specific Implementations
{{#each systemImplementations}}
#### {{systemName}}
{{#each implementations}}
- **{{className}}** - {{description}}
{{/each}}
{{/each}}

## 📝 Usage Examples

### Basic Implementation
```csharp
{{basicImplementationExample}}
```

### Advanced Usage
```csharp
{{advancedUsageExample}}
```

## 🔗 Related Interfaces

{{#each relatedInterfaces}}
- [{{name}}]({{link}}) - {{relationship}}
{{/each}}

## 📝 Design Notes

{{#each designNotes}}
- {{note}}
{{/each}}

---

*Generated on {{generatedDate}} from source code*  
*Last modified: {{lastModified}}*
