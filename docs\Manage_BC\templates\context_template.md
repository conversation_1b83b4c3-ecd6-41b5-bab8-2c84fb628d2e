# {{contextName}} Database Context

## 📋 Overview

**Namespace**: `{{namespace}}`  
**Source File**: `{{filePath}}`  
**Database Schema**: `{{defaultSchema}}`  
**Purpose**: {{purpose}}

{{description}}

## 🏗️ Context Definition

```csharp
{{contextDefinition}}
```

## 🗃️ DbSets

| DbSet | Entity Type | Table Name | Schema | Description |
|-------|-------------|------------|--------|-------------|
{{#each dbSets}}
| `{{name}}` | {{entityType}} | {{tableName}} | {{schema}} | {{description}} |
{{/each}}

## ⚙️ Configuration

### Connection Configuration
```csharp
{{connectionConfiguration}}
```

### Model Building
```csharp
{{modelBuildingConfiguration}}
```

## 🔧 Entity Configurations

{{#each entityConfigurations}}
### {{entityName}}
**Configuration Class**: `{{configurationClass}}`  
**Applied via**: `{{applicationMethod}}`

```csharp
{{configurationCode}}
```

#### Key Features
{{#each features}}
- {{feature}}
{{/each}}
{{/each}}

## 🗂️ Schema Organization

### Default Schema
- **Name**: `{{defaultSchema}}`
- **Purpose**: {{defaultSchemaPurpose}}

### Additional Schemas
{{#each additionalSchemas}}
- **{{name}}**: {{purpose}}
  - **Tables**: {{tableCount}}
  - **Primary Entities**: {{primaryEntities}}
{{/each}}

## 🔗 Relationships

### Cross-Schema Relationships
{{#each crossSchemaRelationships}}
- **{{sourceEntity}}** ({{sourceSchema}}) → **{{targetEntity}}** ({{targetSchema}})
  - **Type**: {{relationshipType}}
  - **Foreign Key**: {{foreignKey}}
{{/each}}

### Complex Relationships
{{#each complexRelationships}}
- **{{name}}**: {{description}}
  - **Entities Involved**: {{entities}}
  - **Relationship Pattern**: {{pattern}}
{{/each}}

## 📊 Performance Considerations

### Indexes
{{#each performanceIndexes}}
- **{{indexName}}**: {{description}}
  - **Columns**: {{columns}}
  - **Type**: {{indexType}}
{{/each}}

### Query Optimization
{{#each queryOptimizations}}
- {{optimization}}
{{/each}}

## 🔄 Migration Support

### Migration History
{{#if migrationSupport}}
- **Enabled**: Yes
- **Migration Table**: {{migrationTable}}
- **Current Version**: {{currentVersion}}
{{else}}
- **Enabled**: No
{{/if}}

### Schema Evolution
{{#each schemaEvolutions}}
- **Version {{version}}**: {{description}}
  - **Changes**: {{changes}}
{{/each}}

## 🧪 Usage Examples

### Basic Context Usage
```csharp
{{basicUsageExample}}
```

### Advanced Querying
```csharp
{{advancedQueryExample}}
```

### Transaction Management
```csharp
{{transactionExample}}
```

## 🔗 Related Contexts

{{#each relatedContexts}}
- [{{name}}]({{link}}) - {{relationship}}
{{/each}}

## 📝 Notes

{{#each notes}}
- {{note}}
{{/each}}

---

*Generated on {{generatedDate}} from source code*  
*Last modified: {{lastModified}}*
