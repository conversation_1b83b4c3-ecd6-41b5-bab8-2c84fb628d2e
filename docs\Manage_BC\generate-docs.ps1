# Generate Documentation Script for Manage-BC Integration
# This script uses DocFX to generate comprehensive documentation

param(
    [switch]$Serve,
    [switch]$Clean,
    [switch]$Watch
)

Write-Host "🚀 Manage-BC Documentation Generator" -ForegroundColor Green
Write-Host "=====================================" -ForegroundColor Green

# Check if DocFX is installed
try {
    $docfxVersion = docfx --version
    Write-Host "✅ DocFX found: $docfxVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ DocFX not found. Installing..." -ForegroundColor Red
    Write-Host "Run: dotnet tool install -g docfx" -ForegroundColor Yellow
    exit 1
}

# Navigate to documentation directory
$docPath = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $docPath

Write-Host "📁 Working directory: $docPath" -ForegroundColor Cyan

# Clean previous build if requested
if ($Clean) {
    Write-Host "🧹 Cleaning previous build..." -ForegroundColor Yellow
    if (Test-Path "_site") {
        Remove-Item "_site" -Recurse -Force
    }
    if (Test-Path "api") {
        Remove-Item "api" -Recurse -Force
    }
}

try {
    # Generate API metadata from source code
    Write-Host "📊 Generating API metadata from source code..." -ForegroundColor Cyan
    docfx metadata docfx.json
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "⚠️  Warning: Some API metadata generation issues occurred" -ForegroundColor Yellow
    } else {
        Write-Host "✅ API metadata generated successfully" -ForegroundColor Green
    }

    # Build documentation
    Write-Host "🔨 Building documentation site..." -ForegroundColor Cyan
    docfx build docfx.json
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Documentation build failed" -ForegroundColor Red
        exit 1
    } else {
        Write-Host "✅ Documentation built successfully" -ForegroundColor Green
    }

    # Serve documentation if requested
    if ($Serve) {
        Write-Host "🌐 Starting documentation server..." -ForegroundColor Cyan
        Write-Host "📖 Documentation will be available at: http://localhost:8080" -ForegroundColor Green
        Write-Host "Press Ctrl+C to stop the server" -ForegroundColor Yellow
        docfx serve _site
    } elseif ($Watch) {
        Write-Host "👀 Starting watch mode..." -ForegroundColor Cyan
        Write-Host "📖 Documentation will be available at: http://localhost:8080" -ForegroundColor Green
        Write-Host "Press Ctrl+C to stop watching" -ForegroundColor Yellow
        docfx docfx.json --serve
    } else {
        Write-Host "✅ Documentation generated successfully!" -ForegroundColor Green
        Write-Host "📁 Output location: $docPath\_site" -ForegroundColor Cyan
        Write-Host "🌐 To serve locally, run: docfx serve _site" -ForegroundColor Yellow
    }

} catch {
    Write-Host "❌ Error occurred: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "" -ForegroundColor White
Write-Host "📚 Documentation Generation Complete!" -ForegroundColor Green
Write-Host "=====================================" -ForegroundColor Green
