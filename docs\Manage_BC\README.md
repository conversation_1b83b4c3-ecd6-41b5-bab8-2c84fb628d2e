# Manage-BC Data Transfer Documentation

This documentation provides comprehensive details about all entities and their properties involved in data transfer between the Manage and Business Central (BC) systems, as well as other integrated systems including Sycle, Simply, Cosium, and PIM.

## 📋 Table of Contents

1. [Overview](01_Overview.md) - System overview and data flow architecture
2. [Core Interfaces](02_Core_Interfaces.md) - Base interfaces and abstractions
3. [Master Data Entities](03_Master_Data_Entities.md) - Products, Clinics, Patients, etc.
4. [Transaction Entities](04_Transaction_Entities.md) - Orders, Invoices, Receipts, etc.
5. [Entity Configurations](05_Entity_Configurations.md) - Database mappings and constraints
6. [Adapters and Mappings](06_Adapters_And_Mappings.md) - Data transformation logic
7. [Event Processing](07_Event_Processing.md) - Event-driven data processing
8. [Data Flow Diagrams](08_Data_Flow_Diagrams.md) - Visual representation of data flows

## 🎯 Purpose

This documentation serves as a comprehensive reference for:

- **Developers** working on the integration between Manage and BC systems
- **Business Analysts** understanding data structures and constraints
- **QA Engineers** validating data transfer processes
- **System Architects** designing integration patterns

## 🔄 Enhanced Auto-Update with docxf

This documentation is configured to automatically update when source code changes using [docxf](https://github.com/docxf/docxf). The enhanced configuration in `docxf.json` provides comprehensive coverage of all integration systems.

### Enhanced Features

#### 🏗️ Multi-System Coverage
- **Integration Model**: Core entities and interfaces
- **Manage Integration**: Manage-specific entities and event processing
- **Sycle Integration**: Sycle practice management entities
- **Simply Integration**: Simply accounting system entities
- **Cosium Integration**: Cosium system entities
- **PIM Integration**: Product Information Management entities
- **Database Schemas**: SQL table definitions and constraints

#### 📊 Comprehensive Documentation
- **Detailed Constraint Analysis**: All validation rules, business constraints, and database constraints
- **Cross-System Relationships**: How entities relate across different systems
- **Integration Mappings**: Data transformation logic between systems
- **Performance Considerations**: Indexing strategies and optimization notes
- **Validation Rules**: Complete validation attribute and business rule documentation

### Setup Enhanced docxf

```bash
# Install docxf globally
npm install -g docxf

# Navigate to documentation directory
cd docs/Manage_BC

# Generate comprehensive documentation
docxf generate

# Watch for changes and auto-regenerate
docxf watch

# Generate with enhanced constraint analysis
docxf generate --include-constraints --include-cross-references
```

### Configuration Highlights

The enhanced `docxf.json` configuration includes:

- **9 Template Types**: Entity, Adapter, Configuration, Domain Model, Interface, Context, Database Schema
- **7 Source Systems**: All integration projects and database schemas
- **Advanced Metadata Extraction**: Properties, constraints, relationships, validation attributes
- **Cross-System Analysis**: Integration mappings and data flow documentation

## 🏗️ System Architecture

The integration follows a layered architecture:

```
┌─────────────────┐    ┌─────────────────┐
│     Manage      │    │ Business Central│
│    System       │    │      (BC)       │
└─────────┬───────┘    └─────────┬───────┘
          │                      │
          │    ┌─────────────────┐│
          └────┤  Integration    ├┘
               │     Layer       │
               └─────────────────┘
```

## 📊 Key Entity Categories

### Master Data Entities
- **Products**: Product catalog and inventory items across all systems
- **Clinics**: Healthcare facility information (Manage, Sycle, Cosium)
- **Patients**: Customer/patient records (Manage, Sycle, Simply, Cosium)
- **Vendors**: Supplier information (BC, Manage)
- **Categories**: Product categorization (PIM, Integration Model)
- **Colors**: Product color specifications (PIM, Integration Model)
- **Manufacturers**: Product manufacturer information
- **Batteries**: Battery specifications for hearing aids (Sycle)

### Transaction Entities
- **Purchase Orders**: Procurement transactions (BC, Simply)
- **Sales Invoices**: Billing transactions (BC, Simply)
- **Purchase Receipts**: Goods receipt transactions (BC, Simply)
- **Sales Credits**: Credit note transactions (BC, Simply)
- **Order Events**: Manage system order processing events
- **Invoice Events**: Manage system invoice processing events
- **Payments**: Payment processing across systems (Simply, Sycle)

### System-Specific Entities
#### Manage System
- **Category Mappings**: Business logic mappings
- **Inventory Countries**: Geographic configurations
- **Event Processing**: Integration event tracking

#### Sycle System
- **Hearing Aids**: Hearing aid device information
- **Earmolds**: Earmold specifications
- **Services**: Service offerings and procedures
- **Remotes**: Remote control devices

#### Simply System
- **Cash Receipts**: Cash transaction records
- **Sales Headers/Details**: Sales transaction structure
- **Purchase Headers/Details**: Purchase transaction structure

#### Cosium System
- **Tax Groups**: Tax configuration and rates
- **Client Information**: Client management data

#### PIM System
- **Product Specifications**: Detailed product attributes
- **Product Hierarchies**: Product categorization structures

## 🔗 Related Documentation

- [Simply Integration Documentation](../../retail-integration-simply/Documentation/)
- [PIM Integration Documentation](../../retail-integration-pim/)
- [Sycle Integration Documentation](../../retail-integration-sycle/)

## 📝 Contributing

When updating this documentation:

1. Ensure all entity properties are documented with their constraints
2. Include validation rules and business logic
3. Update diagrams when data flow changes
4. Test docxf configuration after structural changes

---

*Last updated: 2025-01-15*
*Auto-generated sections will be updated by docxf when source code changes*
