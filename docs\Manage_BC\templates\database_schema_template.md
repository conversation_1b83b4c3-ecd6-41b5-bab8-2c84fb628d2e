# {{tableName}} Database Schema

## 📋 Overview

**Schema**: `{{schemaName}}`  
**Table**: `{{tableName}}`  
**Source File**: `{{filePath}}`  
**Purpose**: {{purpose}}

{{description}}

## 🏗️ Table Definition

```sql
{{tableDefinition}}
```

## 📊 Columns

| Column | Data Type | Nullable | Default | Constraints | Description |
|--------|-----------|----------|---------|-------------|-------------|
{{#each columns}}
| `{{name}}` | {{dataType}} | {{#if nullable}}✅{{else}}❌{{/if}} | {{defaultValue}} | {{constraints}} | {{description}} |
{{/each}}

## 🔑 Primary Key

{{#if primaryKey}}
- **Constraint Name**: `{{primaryKey.name}}`
- **Columns**: {{primaryKey.columns}}
- **Clustered**: {{#if primaryKey.clustered}}Yes{{else}}No{{/if}}

```sql
{{primaryKey.definition}}
```
{{else}}
*No primary key defined*
{{/if}}

## 🔗 Foreign Keys

{{#each foreignKeys}}
### {{name}}
- **Referenced Table**: `{{referencedSchema}}.{{referencedTable}}`
- **Column Mapping**: {{columnMapping}}
- **Delete Action**: {{deleteAction}}
- **Update Action**: {{updateAction}}

```sql
{{definition}}
```
{{/each}}

## 📇 Indexes

{{#each indexes}}
### {{name}}
- **Type**: {{#if unique}}Unique{{else}}Non-Unique{{/if}} {{indexType}}
- **Columns**: {{columns}}
- **Included Columns**: {{includedColumns}}
- **Filter**: {{filter}}

```sql
{{definition}}
```

#### Performance Impact
- **Estimated Size**: {{estimatedSize}}
- **Usage Pattern**: {{usagePattern}}
{{/each}}

## ✅ Check Constraints

{{#each checkConstraints}}
### {{name}}
- **Expression**: `{{expression}}`
- **Description**: {{description}}

```sql
{{definition}}
```
{{/each}}

## 🔧 Default Constraints

{{#each defaultConstraints}}
### {{name}}
- **Column**: `{{columnName}}`
- **Default Value**: {{defaultValue}}
- **Expression**: `{{expression}}`

```sql
{{definition}}
```
{{/each}}

## 🎯 Unique Constraints

{{#each uniqueConstraints}}
### {{name}}
- **Columns**: {{columns}}
- **Type**: {{constraintType}}

```sql
{{definition}}
```
{{/each}}

## 📊 Data Types & Constraints Summary

### Numeric Columns
{{#each numericColumns}}
- **{{name}}**: {{dataType}} - {{description}}
  {{#if precision}}- Precision: {{precision}}{{/if}}
  {{#if scale}}- Scale: {{scale}}{{/if}}
  {{#if range}}- Range: {{range}}{{/if}}
{{/each}}

### String Columns
{{#each stringColumns}}
- **{{name}}**: {{dataType}} - {{description}}
  {{#if maxLength}}- Max Length: {{maxLength}}{{/if}}
  {{#if collation}}- Collation: {{collation}}{{/if}}
  {{#if pattern}}- Pattern: {{pattern}}{{/if}}
{{/each}}

### Date/Time Columns
{{#each dateTimeColumns}}
- **{{name}}**: {{dataType}} - {{description}}
  {{#if precision}}- Precision: {{precision}}{{/if}}
  {{#if timezone}}- Timezone: {{timezone}}{{/if}}
{{/each}}

## 🔄 Relationships

### Parent Tables
{{#each parentTables}}
- **{{tableName}}** ({{schemaName}})
  - **Relationship**: {{relationshipType}}
  - **Foreign Key**: {{foreignKeyColumn}}
  - **Cascade**: {{cascadeBehavior}}
{{/each}}

### Child Tables
{{#each childTables}}
- **{{tableName}}** ({{schemaName}})
  - **Relationship**: {{relationshipType}}
  - **Foreign Key**: {{foreignKeyColumn}}
  - **Cascade**: {{cascadeBehavior}}
{{/each}}

## 📈 Usage Patterns

### Common Queries
{{#each commonQueries}}
#### {{name}}
```sql
{{query}}
```
- **Purpose**: {{purpose}}
- **Performance**: {{performance}}
{{/each}}

### Indexing Strategy
{{#each indexingStrategies}}
- **{{strategy}}**: {{description}}
  - **Rationale**: {{rationale}}
  - **Impact**: {{impact}}
{{/each}}

## 🔗 Related Tables

{{#each relatedTables}}
- [{{name}}]({{link}}) - {{relationship}}
{{/each}}

## 📝 Design Notes

{{#each designNotes}}
- {{note}}
{{/each}}

---

*Generated on {{generatedDate}} from source code*  
*Last modified: {{lastModified}}*
