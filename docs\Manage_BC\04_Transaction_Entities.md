# Transaction Entities

## 📋 Overview

Transaction entities represent business documents and processes that flow between Manage and Business Central systems. These entities capture the operational activities such as purchasing, sales, and inventory movements.

## 🛒 Purchase Transaction Entities

### PurchaseOrder
**Source**: `integration_solution/wsa-retail-integration-model/Models/PurchaseOrders/PurchaseOrder.cs`
**Entity**: `integration_solution/wsa-retail-integration-model/Models/PurchaseOrders/PurchaseOrderEntity.cs`
**Configuration**: `integration_solution/wsa-retail-integration-model/Models/PurchaseOrders/PurchaseOrderConfiguration.cs`
**Table**: `PurchaseOrder` (Schema: `dbo`)

Purchase order documents for procurement processes between Manage and Business Central systems.

#### Properties

| Property | Type | Required | Max Length | Default | Constraints | Description |
|----------|------|----------|------------|---------|-------------|-------------|
| `Id` | Guid | ✅ | - | `NEWID()` | Primary Key | Unique purchase order identifier |
| `DocumentNumber` | string | ✅ | 20 | - | Not Null | Purchase order document number |
| `AlternateNumber` | string? | ❌ | 50 | - | - | Alternative document reference |
| `VendorId` | Guid? | ❌ | - | - | FK to Vendor | Vendor reference |
| `ClinicId` | Guid? | ❌ | - | - | FK to Clinic | Ordering clinic reference |
| `DocumentDate` | DateTime? | ❌ | - | - | - | Purchase order date |
| `CreatedOn` | DateTime | ✅ | - | `sysutcdatetime()` | UTC | Creation timestamp |
| `ModifiedOn` | DateTime | ✅ | - | Auto-updated | UTC | Last modification timestamp |

#### Business Rules
- `DocumentNumber` must be unique across all purchase orders
- Purchase orders must have at least one line item
- `VendorId` and `ClinicId` should be validated against existing records
- Document date cannot be in the future

#### Database Configuration
```csharp
builder.ToTable("PurchaseOrder", "dbo");
builder.HasKey(b => b.Id);

builder.Property(b => b.Id)
    .HasColumnName("Id")
    .HasColumnType("uniqueidentifier")
    .IsRequired();

builder.Property(b => b.DocumentNumber)
    .HasColumnName("DocumentNumber")
    .HasColumnType("nvarchar(20)")
    .HasMaxLength(20)
    .IsRequired();

builder.Property(b => b.AlternateNumber)
    .HasColumnName("AlternateNumber")
    .HasColumnType("nvarchar(50)")
    .HasMaxLength(50);

builder.Property(b => b.VendorId)
    .HasColumnName("VendorId")
    .HasColumnType("uniqueidentifier");

builder.Property(b => b.ClinicId)
    .HasColumnName("ClinicId")
    .HasColumnType("uniqueidentifier");

builder.Property(b => b.DocumentDate)
    .HasColumnName("DocumentDate")
    .HasColumnType("datetime2");
```

#### Relationships
- **Vendor**: Many-to-One via `VendorId`
- **Clinic**: Many-to-One via `ClinicId`
- **Lines**: One-to-Many with `PurchaseOrderLineEntity`

---

### PurchaseOrderLine
**Source**: `integration_solution/wsa-retail-integration-model/Models/PurchaseOrders/PurchaseOrderLineEntity.cs`
**Table**: `PurchaseOrderLine` (Schema: `dbo`)

Individual line items within purchase orders, containing product and quantity information.

#### Properties

| Property | Type | Required | Max Length | Default | Constraints | Description |
|----------|------|----------|------------|---------|-------------|-------------|
| `Id` | Guid | ✅ | - | `NEWID()` | Primary Key | Unique line identifier |
| `PurchaseOrderId` | Guid | ✅ | - | - | FK to PurchaseOrder | Parent purchase order |
| `ProductId` | Guid? | ❌ | - | - | FK to Product | Product reference |
| `Quantity` | decimal | ✅ | - | - | > 0 | Ordered quantity |
| `UnitCost` | decimal? | ❌ | - | - | >= 0 | Unit cost per item |
| `LineAmount` | decimal? | ❌ | - | - | >= 0 | Total line amount |
| `Description` | string? | ❌ | 255 | - | - | Line item description |

#### Business Rules
- `Quantity` must be greater than zero
- `UnitCost` and `LineAmount` must be non-negative when specified
- `LineAmount` should equal `Quantity * UnitCost` when both are provided
- Each line must reference a valid product or have a description

---

## 📄 Event Processing Entities

### OrderEvent
**Source**: `integration_solution/retail-integration-manage/Manage/Models/Orders/OrderEventEntity.cs`
**Table**: `OrderEvent` (Schema: `manage`)

Order event entities for tracking order-related events from Manage system.

#### Properties

| Property | Type | Required | Max Length | Default | Constraints | Description |
|----------|------|----------|------------|---------|-------------|-------------|
| `Id` | Guid | ✅ | - | `NEWID()` | Primary Key | Unique event identifier |
| `ManageId` | long | ✅ | - | - | Not Null | Manage system order ID |
| `OrderNumber` | string? | ❌ | 50 | - | - | Order number from Manage |
| `OrderId` | Guid? | ❌ | - | - | - | Order identifier |
| `LocationId` | Guid? | ❌ | - | - | - | Location/clinic identifier |
| `PatientId` | Guid? | ❌ | - | - | - | Patient identifier |
| `FunderId` | Guid? | ❌ | - | - | - | Funder identifier |
| `BlobPath` | string? | ❌ | 255 | - | - | Event data blob storage path |
| `Status` | int | ✅ | - | - | Enum values | Processing status |
| `ProcessedOn` | DateTime? | ❌ | - | - | UTC | Processing completion time |
| `CreatedOn` | DateTime | ✅ | - | `sysutcdatetime()` | UTC | Creation timestamp |
| `ModifiedOn` | DateTime | ✅ | - | Auto-updated | UTC | Last modification timestamp |

#### Business Rules
- `ManageId` must be unique for order events
- `Status` follows defined enum values for processing states
- Events are processed asynchronously with status tracking
- `ProcessedOn` is set when event processing completes

#### Database Configuration
```csharp
builder.ToTable("OrderEvent");
builder.HasKey(x => x.Id);
builder.ConfigureManageIdField();
builder.ConfigureManageEventMetadataFields();
builder.ConfigureManageOrderIdentifiersFields();
builder.ConfigureManageLocationScopedField();
builder.ConfigureManagePatientScopedField();
builder.ConfigureManageFunderScopedField();
builder.ConfigureManageEventProcessingInfoFields();
builder.ConfigureAuditInfoFields();
```

---

### InvoiceEvent
**Source**: `integration_solution/retail-integration-manage/Manage/Models/Invoices/InvoiceEventEntity.cs`
**Table**: `InvoiceEvent` (Schema: `manage`)

Invoice event entities for tracking invoice-related events from Manage system.

#### Properties

| Property | Type | Required | Max Length | Default | Constraints | Description |
|----------|------|----------|------------|---------|-------------|-------------|
| `Id` | Guid | ✅ | - | `NEWID()` | Primary Key | Unique event identifier |
| `ManageId` | long | ✅ | - | - | Not Null | Manage system invoice ID |
| `InvoiceNumber` | string? | ❌ | 50 | - | - | Invoice number from Manage |
| `InvoiceId` | Guid? | ❌ | - | - | - | Invoice identifier |
| `LocationId` | Guid? | ❌ | - | - | - | Location/clinic identifier |
| `PatientId` | Guid? | ❌ | - | - | - | Patient identifier |
| `FunderId` | Guid? | ❌ | - | - | - | Funder identifier |
| `BlobPath` | string? | ❌ | 255 | - | - | Event data blob storage path |
| `Status` | int | ✅ | - | - | Enum values | Processing status |
| `ProcessedOn` | DateTime? | ❌ | - | - | UTC | Processing completion time |
| `CreatedOn` | DateTime | ✅ | - | `sysutcdatetime()` | UTC | Creation timestamp |
| `ModifiedOn` | DateTime | ✅ | - | Auto-updated | UTC | Last modification timestamp |

#### Business Rules
- `ManageId` must be unique for invoice events
- `Status` follows defined enum values for processing states
- Events are processed asynchronously with status tracking
- Invoice events are linked to order events through business logic

---

## 📄 Event Processing Entities

### OrderEvent
**Source**: `integration_solution/retail-integration-manage/Manage/Models/Orders/OrderEventEntity.cs`
**Configuration**: `integration_solution/retail-integration-manage/Manage/Models/Orders/OrderEventConfiguration.cs`
**Table**: `OrderEvent` (Schema: `manage`)

Order event entities for tracking order-related events from Manage system.

#### Properties

| Property | Type | Required | Max Length | Default | Constraints | Description |
|----------|------|----------|------------|---------|-------------|-------------|
| `Id` | Guid | ✅ | - | `NEWID()` | Primary Key | Unique event identifier |
| `ManageId` | long | ✅ | - | - | Not Null | Manage system order ID |
| `OrderNumber` | string? | ❌ | 50 | - | - | Order number from Manage |
| `OrderId` | Guid? | ❌ | - | - | - | Order identifier |
| `LocationId` | Guid? | ❌ | - | - | - | Location/clinic identifier |
| `PatientId` | Guid? | ❌ | - | - | - | Patient identifier |
| `FunderId` | Guid? | ❌ | - | - | - | Funder identifier |
| `BlobPath` | string? | ❌ | 255 | - | - | Event data blob storage path |
| `Status` | int | ✅ | - | - | Enum values | Processing status |
| `ProcessedOn` | DateTime? | ❌ | - | - | UTC | Processing completion time |
| `CreatedOn` | DateTime | ✅ | - | `sysutcdatetime()` | UTC | Creation timestamp |
| `ModifiedOn` | DateTime | ✅ | - | Auto-updated | UTC | Last modification timestamp |

#### Business Rules
- `ManageId` must be unique for order events
- `Status` follows defined enum values for processing states
- Events are processed asynchronously with status tracking
- `ProcessedOn` is set when event processing completes

#### Database Configuration
```csharp
builder.ToTable("OrderEvent");
builder.HasKey(x => x.Id);
builder.ConfigureManageIdField();
builder.ConfigureManageEventMetadataFields();
builder.ConfigureManageOrderIdentifiersFields();
builder.ConfigureManageLocationScopedField();
builder.ConfigureManagePatientScopedField();
builder.ConfigureManageFunderScopedField();
builder.ConfigureManageEventProcessingInfoFields();
builder.ConfigureAuditInfoFields();
```

---

### InvoiceEvent
**Source**: `integration_solution/retail-integration-manage/Manage/Models/Invoices/InvoiceEventEntity.cs`
**Configuration**: `integration_solution/retail-integration-manage/Manage/Models/Invoices/InvoiceEventConfiguration.cs`
**Table**: `InvoiceEvent` (Schema: `manage`)

Invoice event entities for tracking invoice-related events from Manage system.

#### Properties

| Property | Type | Required | Max Length | Default | Constraints | Description |
|----------|------|----------|------------|---------|-------------|-------------|
| `Id` | Guid | ✅ | - | `NEWID()` | Primary Key | Unique event identifier |
| `ManageId` | long | ✅ | - | - | Not Null | Manage system invoice ID |
| `InvoiceNumber` | string? | ❌ | 50 | - | - | Invoice number from Manage |
| `InvoiceId` | Guid? | ❌ | - | - | - | Invoice identifier |
| `LocationId` | Guid? | ❌ | - | - | - | Location/clinic identifier |
| `PatientId` | Guid? | ❌ | - | - | - | Patient identifier |
| `FunderId` | Guid? | ❌ | - | - | - | Funder identifier |
| `BlobPath` | string? | ❌ | 255 | - | - | Event data blob storage path |
| `Status` | int | ✅ | - | - | Enum values | Processing status |
| `ProcessedOn` | DateTime? | ❌ | - | - | UTC | Processing completion time |
| `CreatedOn` | DateTime | ✅ | - | `sysutcdatetime()` | UTC | Creation timestamp |
| `ModifiedOn` | DateTime | ✅ | - | Auto-updated | UTC | Last modification timestamp |

#### Business Rules
- `ManageId` must be unique for invoice events
- `Status` follows defined enum values for processing states
- Events are processed asynchronously with status tracking
- Invoice events are linked to order events through business logic

#### Database Configuration
```csharp
builder.ToTable("InvoiceEvent");
builder.HasKey(x => x.Id);
builder.ConfigureManageIdField();
builder.ConfigureManageEventMetadataFields();
builder.ConfigureManageInvoiceIdentifiersFields();
builder.ConfigureManageLocationScopedField();
builder.ConfigureManagePatientScopedField();
builder.ConfigureManageFunderScopedField();
builder.ConfigureManageEventProcessingInfoFields();
builder.ConfigureAuditInfoFields();
```

---

### OrderLineItemAcceptedEvent
**Source**: `integration_solution/retail-integration-manage/Manage/Models/Orders/OrderLineItemAcceptedEventEntity.cs`
**Table**: `OrderLineItemAcceptedEvent` (Schema: `manage`)

Order line item acceptance events for tracking individual line item processing.

#### Properties

| Property | Type | Required | Max Length | Default | Constraints | Description |
|----------|------|----------|------------|---------|-------------|-------------|
| `Id` | Guid | ✅ | - | `NEWID()` | Primary Key | Unique event identifier |
| `LineItemId` | Guid | ✅ | - | - | Not Null | Order line item identifier |
| `OrderId` | Guid? | ❌ | - | - | - | Parent order identifier |
| `ProductId` | Guid? | ❌ | - | - | - | Product identifier |
| `Quantity` | decimal? | ❌ | - | - | >= 0 | Accepted quantity |
| `BlobPath` | string? | ❌ | 255 | - | - | Event data blob storage path |
| `Status` | int | ✅ | - | - | Enum values | Processing status |
| `ProcessedOn` | DateTime? | ❌ | - | - | UTC | Processing completion time |
| `CreatedOn` | DateTime | ✅ | - | `sysutcdatetime()` | UTC | Creation timestamp |
| `ModifiedOn` | DateTime | ✅ | - | Auto-updated | UTC | Last modification timestamp |

#### Business Rules
- `LineItemId` must be unique for line item events
- `Quantity` must be non-negative when specified
- Line item events are processed after parent order events
- Acceptance events trigger inventory and fulfillment processes

---

### SalesEntity
**Source**: `integration_solution/retail-integration-manage/Manage/Models/Sales/SalesEntity.cs`
**Table**: `Sales` (Schema: `manage`)

Sales transaction entities for tracking completed sales from Manage system.

#### Properties

| Property | Type | Required | Max Length | Default | Constraints | Description |
|----------|------|----------|------------|---------|-------------|-------------|
| `Id` | Guid | ✅ | - | `NEWID()` | Primary Key | Unique sales identifier |
| `ManageId` | long | ✅ | - | - | Not Null | Manage system sales ID |
| `SalesNumber` | string? | ❌ | 50 | - | - | Sales transaction number |
| `LocationId` | Guid? | ❌ | - | - | - | Location/clinic identifier |
| `PatientId` | Guid? | ❌ | - | - | - | Patient identifier |
| `TotalAmount` | decimal? | ❌ | - | - | >= 0 | Total sales amount |
| `SalesDate` | DateTime? | ❌ | - | - | - | Sales transaction date |
| `CreatedOn` | DateTime | ✅ | - | `sysutcdatetime()` | UTC | Creation timestamp |
| `ModifiedOn` | DateTime | ✅ | - | Auto-updated | UTC | Last modification timestamp |

#### Business Rules
- `ManageId` must be unique for sales records
- `TotalAmount` must be non-negative when specified
- Sales records are created after successful order fulfillment
- Sales data is used for reporting and analytics

| Property | Type | Required | Max Length | Constraints | Description |
|----------|------|----------|------------|-------------|-------------|
| `Id` | Guid | ✅ | - | Primary Key | Unique purchase order identifier |
| `DocumentNumber` | string? | ❌ | 50 | Unique | Purchase order number |
| `DocumentDate` | DateOnly? | ❌ | - | - | Purchase order date |
| `VendorId` | Guid? | ❌ | - | FK to Vendor | Supplier reference |
| `ClinicId` | Guid? | ❌ | - | FK to Clinic | Ordering clinic reference |
| `Status` | string? | ❌ | 20 | Enum values | Order status (Draft, Submitted, Approved, etc.) |
| `TotalAmount` | decimal? | ❌ | - | >= 0 | Total order amount |
| `Currency` | string? | ❌ | 3 | ISO currency | Currency code (USD, EUR, etc.) |
| `ExternalSystemCode` | string? | ❌ | 20 | - | Source system identifier |
| `ExternalCode` | string? | ❌ | 50 | - | External system order ID |
| `CreatedOn` | DateTime | ✅ | - | UTC | Creation timestamp |
| `ModifiedOn` | DateTime | ✅ | - | UTC | Last modification timestamp |

#### Business Rules
- Purchase orders must have either `DocumentNumber` or `ExternalCode`
- `TotalAmount` must be non-negative when specified
- Status transitions follow defined workflow
- Synchronized bidirectionally between Manage and BC

### Purchase Order Line
**Source**: `integration_solution/wsa-retail-integration-model/Models/PurchaseOrders/PurchaseOrderLine.cs`
**Entity**: `integration_solution/wsa-retail-integration-model/Models/PurchaseOrders/PurchaseOrderLineEntity.cs`

Line items for purchase orders.

#### Properties

| Property | Type | Required | Max Length | Constraints | Description |
|----------|------|----------|------------|-------------|-------------|
| `Id` | Guid | ✅ | - | Primary Key | Unique line identifier |
| `PurchaseOrderId` | Guid | ✅ | - | FK to PurchaseOrder | Parent order reference |
| `ProductId` | Guid? | ❌ | - | FK to Product | Product reference |
| `LineNumber` | int? | ❌ | - | > 0 | Line sequence number |
| `Quantity` | decimal? | ❌ | - | > 0 | Ordered quantity |
| `UnitPrice` | decimal? | ❌ | - | >= 0 | Price per unit |
| `LineAmount` | decimal? | ❌ | - | >= 0 | Total line amount |
| `Description` | string? | ❌ | 255 | - | Line item description |
| `CreatedOn` | DateTime | ✅ | - | UTC | Creation timestamp |
| `ModifiedOn` | DateTime | ✅ | - | UTC | Last modification timestamp |

#### Business Rules
- Each line must belong to a purchase order
- `Quantity` must be positive when specified
- `LineAmount` typically equals `Quantity * UnitPrice`
- Line numbers should be unique within an order

### Purchase Receipt
**Source**: `integration_solution/wsa-retail-integration-model/Models/PurchaseReceipts/PurchaseReceipt.cs`
**Entity**: `integration_solution/wsa-retail-integration-model/Models/PurchaseReceipts/PurchaseReceiptEntity.cs`
**Configuration**: `integration_solution/wsa-retail-integration-model/Models/PurchaseReceipts/PurchaseReceiptEntityConfiguration.cs`

Goods receipt documents for received inventory.

#### Properties

| Property | Type | Required | Max Length | Constraints | Description |
|----------|------|----------|------------|-------------|-------------|
| `Id` | Guid | ✅ | - | Primary Key | Unique receipt identifier |
| `DocumentNumber` | string? | ❌ | 50 | Unique | Receipt document number |
| `DocumentDate` | DateOnly? | ❌ | - | - | Receipt date |
| `PurchaseOrderId` | Guid? | ❌ | - | FK to PurchaseOrder | Related purchase order |
| `VendorId` | Guid? | ❌ | - | FK to Vendor | Supplier reference |
| `ClinicId` | Guid? | ❌ | - | FK to Clinic | Receiving clinic reference |
| `Status` | string? | ❌ | 20 | Enum values | Receipt status |
| `TotalAmount` | decimal? | ❌ | - | >= 0 | Total receipt amount |
| `ExternalSystemCode` | string? | ❌ | 20 | - | Source system identifier |
| `ExternalCode` | string? | ❌ | 50 | - | External system receipt ID |
| `CreatedOn` | DateTime | ✅ | - | UTC | Creation timestamp |
| `ModifiedOn` | DateTime | ✅ | - | UTC | Last modification timestamp |

#### Business Rules
- Receipts can be linked to purchase orders
- Receipt quantities cannot exceed ordered quantities
- Synchronized bidirectionally between Manage and BC

## 💰 Sales Transaction Entities

### Sales Invoice
**Source**: `integration_solution/wsa-retail-integration-model/Models/SalesInvoices/SalesInvoice.cs`
**Entity**: `integration_solution/wsa-retail-integration-model/Models/SalesInvoices/SalesInvoiceEntity.cs`
**Configuration**: `integration_solution/wsa-retail-integration-model/Models/SalesInvoices/SalesInvoiceEntityConfiguration.cs`

Customer billing documents.

#### Properties

| Property | Type | Required | Max Length | Constraints | Description |
|----------|------|----------|------------|-------------|-------------|
| `Id` | Guid | ✅ | - | Primary Key | Unique invoice identifier |
| `DocumentNumber` | string? | ❌ | 50 | Unique | Invoice number |
| `DocumentDate` | DateOnly? | ❌ | - | - | Invoice date |
| `PatientId` | Guid? | ❌ | - | FK to Patient | Customer reference |
| `ClinicId` | Guid? | ❌ | - | FK to Clinic | Billing clinic reference |
| `PayorId` | Guid? | ❌ | - | FK to Payor | Insurance/payor reference |
| `Status` | string? | ❌ | 20 | Enum values | Invoice status |
| `TotalAmount` | decimal? | ❌ | - | >= 0 | Total invoice amount |
| `TaxAmount` | decimal? | ❌ | - | >= 0 | Total tax amount |
| `Currency` | string? | ❌ | 3 | ISO currency | Currency code |
| `DueDate` | DateOnly? | ❌ | - | >= DocumentDate | Payment due date |
| `ExternalSystemCode` | string? | ❌ | 20 | - | Source system identifier |
| `ExternalCode` | string? | ❌ | 50 | - | External system invoice ID |
| `CreatedOn` | DateTime | ✅ | - | UTC | Creation timestamp |
| `ModifiedOn` | DateTime | ✅ | - | UTC | Last modification timestamp |

#### Business Rules
- Invoices must have either `DocumentNumber` or `ExternalCode`
- `DueDate` must be on or after `DocumentDate`
- `TotalAmount` includes `TaxAmount`
- Synchronized bidirectionally between Manage and BC

### Sales Invoice Line
**Source**: `integration_solution/wsa-retail-integration-model/Models/SalesInvoices/SalesInvoiceLine.cs`
**Entity**: `integration_solution/wsa-retail-integration-model/Models/SalesInvoices/SalesInvoiceLineEntity.cs`

Line items for sales invoices.

#### Properties

| Property | Type | Required | Max Length | Constraints | Description |
|----------|------|----------|------------|-------------|-------------|
| `Id` | Guid | ✅ | - | Primary Key | Unique line identifier |
| `SalesInvoiceId` | Guid | ✅ | - | FK to SalesInvoice | Parent invoice reference |
| `ProductId` | Guid? | ❌ | - | FK to Product | Product reference |
| `LineNumber` | int? | ❌ | - | > 0 | Line sequence number |
| `Quantity` | decimal? | ❌ | - | > 0 | Invoiced quantity |
| `UnitPrice` | decimal? | ❌ | - | >= 0 | Price per unit |
| `LineAmount` | decimal? | ❌ | - | >= 0 | Total line amount |
| `TaxAmount` | decimal? | ❌ | - | >= 0 | Line tax amount |
| `TaxGroupId` | Guid? | ❌ | - | FK to TaxGroup | Tax classification |
| `Description` | string? | ❌ | 255 | - | Line item description |
| `SerialNumber` | string? | ❌ | 50 | - | Product serial number |
| `CreatedOn` | DateTime | ✅ | - | UTC | Creation timestamp |
| `ModifiedOn` | DateTime | ✅ | - | UTC | Last modification timestamp |

#### Business Rules
- Each line must belong to a sales invoice
- `Quantity` must be positive when specified
- Serial numbers required for serialized products
- Tax calculations based on `TaxGroupId`

### Sales Credit
**Source**: `integration_solution/wsa-retail-integration-model/Models/SalesCredits/SalesCredit.cs`
**Entity**: `integration_solution/wsa-retail-integration-model/Models/SalesCredits/SalesCreditEntity.cs`
**Configuration**: `integration_solution/wsa-retail-integration-model/Models/SalesCredits/SalesCreditEntityConfiguration.cs`

Credit note documents for returns and adjustments.

#### Properties

| Property | Type | Required | Max Length | Constraints | Description |
|----------|------|----------|------------|-------------|-------------|
| `Id` | Guid | ✅ | - | Primary Key | Unique credit identifier |
| `DocumentNumber` | string? | ❌ | 50 | Unique | Credit note number |
| `DocumentDate` | DateOnly? | ❌ | - | - | Credit date |
| `PatientId` | Guid? | ❌ | - | FK to Patient | Customer reference |
| `ClinicId` | Guid? | ❌ | - | FK to Clinic | Issuing clinic reference |
| `OriginalInvoiceId` | Guid? | ❌ | - | FK to SalesInvoice | Original invoice reference |
| `Status` | string? | ❌ | 20 | Enum values | Credit status |
| `TotalAmount` | decimal? | ❌ | - | >= 0 | Total credit amount |
| `TaxAmount` | decimal? | ❌ | - | >= 0 | Total tax credit |
| `Currency` | string? | ❌ | 3 | ISO currency | Currency code |
| `Reason` | string? | ❌ | 255 | - | Credit reason/description |
| `ExternalSystemCode` | string? | ❌ | 20 | - | Source system identifier |
| `ExternalCode` | string? | ❌ | 50 | - | External system credit ID |
| `CreatedOn` | DateTime | ✅ | - | UTC | Creation timestamp |
| `ModifiedOn` | DateTime | ✅ | - | UTC | Last modification timestamp |

#### Business Rules
- Credits can reference original invoices
- Credit amounts are typically positive (representing credit value)
- Synchronized bidirectionally between Manage and BC

---

*This document covers the main transaction entities. Additional specialized entities and their relationships are documented in the entity configurations section.*
