# {{className}} Entity

## 📋 Overview

**Namespace**: `{{namespace}}`
**Source File**: `{{filePath}}`
**Table**: `{{tableName}}`
**Schema**: `{{schemaName}}`

{{description}}

## 🏗️ Entity Structure

### Class Definition
```csharp
{{classDefinition}}
```

## 📊 Properties

| Property | Type | Required | Max Length | Default Value | Constraints | Description |
|----------|------|----------|------------|---------------|-------------|-------------|
{{#each properties}}
| `{{name}}` | {{type}} | {{#if required}}✅{{else}}❌{{/if}} | {{maxLength}} | {{defaultValue}} | {{constraints}} | {{description}} |
{{/each}}

## 🔗 Relationships

### Foreign Keys
{{#each foreignKeys}}
- **{{propertyName}}** → `{{referencedTable}}.{{referencedColumn}}`
  - **Relationship**: {{relationshipType}}
  - **Delete Behavior**: {{deleteBehavior}}
  - **Required**: {{#if required}}Yes{{else}}No{{/if}}
{{/each}}

### Navigation Properties
{{#each navigationProperties}}
- **{{name}}** ({{type}})
  - **Relationship**: {{relationshipType}}
  - **Foreign Key**: {{foreignKeyProperty}}
{{/each}}

## 🗂️ Database Configuration

### Table Configuration
```csharp
builder.ToTable("{{tableName}}"{{#if schemaName}}, "{{schemaName}}"{{/if}});
```

### Primary Key
```csharp
builder.HasKey({{primaryKeyExpression}});
```

### Column Configurations
{{#each columnConfigurations}}
```csharp
builder.Property({{propertyExpression}})
    .HasColumnName("{{columnName}}")
    .HasColumnType("{{columnType}}")
    {{#if maxLength}}.HasMaxLength({{maxLength}}){{/if}}
    {{#if required}}.IsRequired(){{else}}.IsRequired(false){{/if}}
    {{#if defaultValue}}.HasDefaultValue({{defaultValue}}){{/if}}
    {{#if defaultValueSql}}.HasDefaultValueSql("{{defaultValueSql}}"){{/if}};
```
{{/each}}

## 📇 Indexes

{{#each indexes}}
### {{name}}
- **Type**: {{#if unique}}Unique{{else}}Non-Unique{{/if}}
- **Columns**: {{columns}}
- **Database Name**: `{{databaseName}}`

```csharp
builder.HasIndex({{indexExpression}})
    {{#if unique}}.IsUnique(){{/if}}
    .HasDatabaseName("{{databaseName}}");
```
{{/each}}

## ✅ Validation Rules

### Data Annotations
{{#each validationAttributes}}
- **{{attributeName}}**: {{description}}
  {{#if parameters}}- Parameters: {{parameters}}{{/if}}
  {{#if errorMessage}}- Error Message: "{{errorMessage}}"{{/if}}
  {{#if validationLogic}}- Validation Logic: {{validationLogic}}{{/if}}
{{/each}}

### Business Rules
{{#each businessRules}}
- **{{rule}}**
  {{#if condition}}- Condition: {{condition}}{{/if}}
  {{#if enforcement}}- Enforcement: {{enforcement}}{{/if}}
  {{#if exceptions}}- Exceptions: {{exceptions}}{{/if}}
{{/each}}

### Database Constraints
{{#each databaseConstraints}}
- **{{type}}**: {{description}}
  {{#if expression}}- Expression: `{{expression}}`{{/if}}
  {{#if errorMessage}}- Error Message: "{{errorMessage}}"{{/if}}
  {{#if enforcementLevel}}- Enforcement: {{enforcementLevel}}{{/if}}
{{/each}}

### Validation Attributes Detail
{{#each detailedValidations}}
#### {{attributeName}}
- **Property**: `{{propertyName}}`
- **Type**: {{validationType}}
- **Parameters**: {{parameters}}
- **Custom Logic**: {{customLogic}}
- **Error Handling**: {{errorHandling}}

```csharp
{{attributeCode}}
```
{{/each}}

### Cross-Property Validations
{{#each crossPropertyValidations}}
- **{{name}}**: {{description}}
  - **Properties Involved**: {{properties}}
  - **Validation Logic**: {{logic}}
  - **Error Condition**: {{errorCondition}}
{{/each}}

## 🔄 Common Configuration Patterns

{{#if usesMasterDataFields}}
### Master Data Fields
Applied via `ConfigureMasterDataFields()`:
- **Id**: `uniqueidentifier`, Primary Key
- **Code**: `nvarchar(50)`, Required, Unique
- **Name**: `nvarchar(255)`, Optional
- **AlternateCode**: `nvarchar(50)`, Optional
{{/if}}

{{#if usesAddressFields}}
### Address Fields
Applied via `ConfigureAddressFields()`:
- **Address**: `nvarchar(100)`, Optional
- **Address2**: `nvarchar(100)`, Optional
- **City**: `nvarchar(50)`, Optional
- **Region**: `nvarchar(50)`, Optional
- **Country**: `nvarchar(50)`, Optional
- **PostalCode**: `nvarchar(20)`, Optional
{{/if}}

{{#if usesContactFields}}
### Contact Fields
Applied via `ConfigureContactFields()`:
- **Phone**: `nvarchar(20)`, Optional
- **Email**: `nvarchar(100)`, Optional
{{/if}}

{{#if usesAuditFields}}
### Audit Info Fields
Applied via `ConfigureAuditInfoFields()`:
- **CreatedOn**: `datetime2(7)`, Required, Default: `sysutcdatetime()`
- **ModifiedOn**: `datetime2(7)`, Required, Auto-updated
{{/if}}

## 🔍 Detailed Constraint Analysis

### Property-Level Constraints
{{#each propertyConstraints}}
#### {{propertyName}}
- **Data Type**: {{dataType}}
- **Database Type**: {{databaseType}}
- **Nullable**: {{#if nullable}}Yes{{else}}No{{/if}}
- **Max Length**: {{maxLength}}
- **Precision/Scale**: {{precision}}/{{scale}}
- **Default Value**: {{defaultValue}}

**Validation Rules**:
{{#each validationRules}}
- {{rule}}
{{/each}}

**Database Constraints**:
{{#each databaseConstraints}}
- {{constraint}}
{{/each}}

**Business Logic**:
{{#each businessLogic}}
- {{logic}}
{{/each}}
{{/each}}

### Entity-Level Constraints
{{#each entityConstraints}}
- **{{type}}**: {{description}}
  - **Scope**: {{scope}}
  - **Enforcement**: {{enforcement}}
  - **Impact**: {{impact}}
{{/each}}

### Integration Constraints
{{#each integrationConstraints}}
- **{{system}}**: {{constraint}}
  - **Mapping Rule**: {{mappingRule}}
  - **Validation**: {{validation}}
  - **Error Handling**: {{errorHandling}}
{{/each}}

## 📊 Data Quality Rules

### Required Field Validation
{{#each requiredFields}}
- **{{fieldName}}**: {{requirement}}
  - **Business Justification**: {{justification}}
  - **Validation Method**: {{validationMethod}}
{{/each}}

### Data Format Validation
{{#each formatValidations}}
- **{{fieldName}}**: {{format}}
  - **Pattern**: `{{pattern}}`
  - **Examples**: {{examples}}
  - **Error Messages**: {{errorMessages}}
{{/each}}

### Range Validation
{{#each rangeValidations}}
- **{{fieldName}}**: {{range}}
  - **Minimum**: {{minimum}}
  - **Maximum**: {{maximum}}
  - **Business Rule**: {{businessRule}}
{{/each}}

## 📈 Usage Examples

### Entity Creation
```csharp
var entity = new {{className}}
{
    {{#each requiredProperties}}
    {{name}} = {{exampleValue}},
    {{/each}}
};
```

### Validation Example
```csharp
// Validate entity before saving
var validationResults = new List<ValidationResult>();
var validationContext = new ValidationContext(entity);
bool isValid = Validator.TryValidateObject(entity, validationContext, validationResults, true);

if (!isValid)
{
    foreach (var error in validationResults)
    {
        Console.WriteLine($"Validation Error: {error.ErrorMessage}");
    }
}
```

### Query Examples
```csharp
// Find by Code
var entity = context.{{pluralName}}.FirstOrDefault(x => x.Code == "{{exampleCode}}");

// Include related entities
var entityWithRelations = context.{{pluralName}}
    {{#each navigationProperties}}
    .Include(x => x.{{name}})
    {{/each}}
    .FirstOrDefault(x => x.Id == id);

// Complex query with constraints
var filteredEntities = context.{{pluralName}}
    .Where(x => x.{{constraintProperty}} {{constraintOperator}} {{constraintValue}})
    .OrderBy(x => x.{{sortProperty}})
    .ToList();
```

## 🔗 Related Entities

{{#each relatedEntities}}
- [{{name}}]({{link}}) - {{relationship}}
{{/each}}

## 📝 Notes

{{#each notes}}
- {{note}}
{{/each}}

---

*Generated on {{generatedDate}} from source code*
*Last modified: {{lastModified}}*
