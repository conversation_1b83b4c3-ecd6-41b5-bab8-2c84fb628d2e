{
  "metadata": {
    "name": "Manage-BC Data Transfer Documentation",
    "description": "Comprehensive documentation of entities and their properties involved in data transfer between Manage and Business Central (BC) systems",
    "version": "2.0.0"
  },
  "build": {
    "content": [
  "sources": [
    {
      "name": "integration_model",
      "path": "../integration_solution/wsa-retail-integration-model",
      "include": [
        "**/*.cs",
        "**/*.md"
      ],
      "exclude": [
        "**/bin/**",
        "**/obj/**",
        "**/packages/**",
        "**/.vs/**",
        "**/TestResults/**"
      ]
    },
    {
      "name": "manage_integration",
      "path": "../integration_solution/retail-integration-manage",
      "include": [
        "**/*.cs",
        "**/*.md"
      ],
      "exclude": [
        "**/bin/**",
        "**/obj/**",
        "**/packages/**",
        "**/.vs/**",
        "**/TestResults/**"
      ]
    },
    {
      "name": "sycle_integration",
      "path": "../integration_solution/retail-integration-sycle",
      "include": [
        "**/*.cs",
        "**/*.md"
      ],
      "exclude": [
        "**/bin/**",
        "**/obj/**",
        "**/packages/**",
        "**/.vs/**",
        "**/TestResults/**"
      ]
    },
    {
      "name": "simply_integration",
      "path": "../integration_solution/retail-integration-simply",
      "include": [
        "**/*.cs",
        "**/*.md"
      ],
      "exclude": [
        "**/bin/**",
        "**/obj/**",
        "**/packages/**",
        "**/.vs/**",
        "**/TestResults/**"
      ]
    },
    {
      "name": "cosium_integration",
      "path": "../integration_solution/retail-integration-cosium",
      "include": [
        "**/*.cs",
        "**/*.md"
      ],
      "exclude": [
        "**/bin/**",
        "**/obj/**",
        "**/packages/**",
        "**/.vs/**",
        "**/TestResults/**"
      ]
    },
    {
      "name": "pim_integration",
      "path": "../integration_solution/retail-integration-pim",
      "include": [
        "**/*.cs",
        "**/*.md"
      ],
      "exclude": [
        "**/bin/**",
        "**/obj/**",
        "**/packages/**",
        "**/.vs/**",
        "**/TestResults/**"
      ]
    },
    {
      "name": "database_schemas",
      "path": "../integration_solution",
      "include": [
        "**/tables/*.sql",
        "**/schemas/*.sql",
        "**/views/*.sql"
      ],
      "exclude": [
        "**/bin/**",
        "**/obj/**"
      ]
    }
  ],
  "output": {
    "format": "markdown",
    "destination": "./generated"
  },
  "templates": {
    "core_entity": {
      "pattern": "**/Models/**/*Entity.cs",
      "template": "templates/entity_template.md",
      "metadata": {
        "extract": [
          "properties",
          "constraints",
          "relationships",
          "indexes",
          "validation_attributes",
          "data_annotations",
          "foreign_keys",
          "navigation_properties",
          "inheritance_hierarchy",
          "interfaces"
        ]
      }
    },
    "manage_entity": {
      "pattern": "**/Manage/Models/**/*Entity.cs",
      "template": "templates/entity_template.md",
      "metadata": {
        "extract": [
          "properties",
          "constraints",
          "relationships",
          "indexes",
          "validation_attributes",
          "manage_specific_fields",
          "event_processing_fields"
        ]
      }
    },
    "external_system_entity": {
      "pattern": "**/{Sycle,Simply,Cosium}/**/*Entity.cs",
      "template": "templates/entity_template.md",
      "metadata": {
        "extract": [
          "properties",
          "constraints",
          "relationships",
          "integration_fields",
          "system_specific_mappings"
        ]
      }
    },
    "adapter": {
      "pattern": "**/*Adapter.cs",
      "template": "templates/adapter_template.md",
      "metadata": {
        "extract": [
          "methods",
          "transformations",
          "validation_rules",
          "business_logic",
          "interface_implementations",
          "error_handling",
          "data_mappings"
        ]
      }
    },
    "configuration": {
      "pattern": "**/*Configuration.cs",
      "template": "templates/configuration_template.md",
      "metadata": {
        "extract": [
          "table_mappings",
          "column_constraints",
          "indexes",
          "foreign_keys",
          "default_values",
          "schema_definitions",
          "fluent_api_configurations",
          "common_patterns"
        ]
      }
    },
    "domain_model": {
      "pattern": "**/Models/**/*.cs",
      "exclude": ["**/*Entity.cs", "**/*Configuration.cs", "**/*Adapter.cs"],
      "template": "templates/domain_model_template.md",
      "metadata": {
        "extract": [
          "properties",
          "json_attributes",
          "validation_rules",
          "business_rules",
          "interfaces",
          "inheritance",
          "serialization_attributes"
        ]
      }
    },
    "interface": {
      "pattern": "**/I*.cs",
      "template": "templates/interface_template.md",
      "metadata": {
        "extract": [
          "methods",
          "properties",
          "inheritance",
          "generic_constraints",
          "documentation"
        ]
      }
    },
    "context": {
      "pattern": "**/*Context.cs",
      "template": "templates/context_template.md",
      "metadata": {
        "extract": [
          "dbsets",
          "configurations",
          "schema_definitions",
          "connection_strings",
          "model_building"
        ]
      }
    },
    "database_schema": {
      "pattern": "**/*.sql",
      "template": "templates/database_schema_template.md",
      "metadata": {
        "extract": [
          "table_definitions",
          "column_constraints",
          "indexes",
          "foreign_keys",
          "check_constraints",
          "default_values"
        ]
      }
    }
  },
  "watch": {
    "enabled": true,
    "patterns": [
      "**/*.cs"
    ],
    "debounce": 2000
  },
  "metadata": {
    "author": "ERP Integration Team",
    "created": "2025-01-15",
    "updated": "2025-07-15",
    "tags": ["ERP", "Integration", "Manage", "Business Central", "Data Transfer", "Entity Framework", "Constraints", "Sycle", "Simply", "Cosium", "PIM"],
    "version_control": {
      "track_changes": true,
      "include_commit_info": true,
      "include_file_history": true
    },
    "systems": {
      "manage": {
        "description": "Manage system integration for healthcare operations",
        "schema": "manage",
        "primary_entities": ["OrderEvent", "InvoiceEvent", "CategoryMapping", "InventoryCountry"]
      },
      "business_central": {
        "description": "Microsoft Dynamics 365 Business Central ERP system",
        "schema": "dbo",
        "primary_entities": ["Product", "Customer", "Vendor", "SalesOrder", "PurchaseOrder"]
      },
      "sycle": {
        "description": "Sycle practice management system",
        "schema": "sycle",
        "primary_entities": ["Patient", "Clinic", "HearingAid", "Battery", "Service"]
      },
      "simply": {
        "description": "Simply accounting system integration",
        "schema": "simply",
        "primary_entities": ["Customer", "SalesHeader", "PurchaseOrder", "Payment"]
      },
      "cosium": {
        "description": "Cosium system integration",
        "schema": "cosium",
        "primary_entities": ["Clinics", "Patients", "Client", "TauxTVA"]
      },
      "pim": {
        "description": "Product Information Management system",
        "schema": "pim",
        "primary_entities": ["Product", "Category", "Manufacturer", "Specification"]
      }
    }
  },
  "generation": {
    "include_toc": true,
    "include_diagrams": true,
    "cross_reference": true,
    "constraint_analysis": true,
    "include_entity_relationships": true,
    "include_data_flow_diagrams": true,
    "include_validation_summary": true,
    "include_business_rules": true,
    "include_integration_mappings": true,
    "group_by_system": true,
    "include_performance_notes": true,
    "include_migration_notes": true
  },
  "analysis": {
    "constraint_types": [
      "primary_key",
      "foreign_key",
      "unique",
      "check",
      "not_null",
      "default_value",
      "data_type",
      "max_length",
      "precision_scale",
      "validation_attributes",
      "business_rules"
    ],
    "relationship_analysis": {
      "include_cardinality": true,
      "include_cascade_behavior": true,
      "include_referential_integrity": true,
      "include_orphan_detection": true
    },
    "validation_analysis": {
      "include_data_annotations": true,
      "include_fluent_validations": true,
      "include_business_validations": true,
      "include_custom_validators": true
    }
  }
}
