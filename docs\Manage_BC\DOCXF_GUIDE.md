# Enhanced Docxf Configuration Guide

## 📋 Overview

This guide explains how to use the enhanced docxf configuration to generate comprehensive documentation for all entities and their properties with detailed constraints across the entire ERP integration solution.

## 🚀 Quick Start

### Prerequisites

```bash
# Install Node.js (if not already installed)
# Download from https://nodejs.org/

# Install docxf globally
npm install -g docxf

# Verify installation
docxf --version
```

### Basic Usage

```bash
# Navigate to the documentation directory
cd docs/Manage_BC

# Generate all documentation
docxf generate

# Watch for changes and auto-regenerate
docxf watch

# Generate specific templates only
docxf generate --template entity
docxf generate --template adapter
docxf generate --template configuration
```

## 🏗️ Configuration Structure

### Source Coverage

The enhanced configuration covers:

1. **Integration Model** (`wsa-retail-integration-model`)
   - Core entities and interfaces
   - Base configurations and patterns

2. **Manage Integration** (`retail-integration-manage`)
   - Manage-specific entities
   - Event processing logic
   - Category mappings

3. **Sycle Integration** (`retail-integration-sycle`)
   - Practice management entities
   - Healthcare-specific models

4. **Simply Integration** (`retail-integration-simply`)
   - Accounting system entities
   - Financial transaction models

5. **Cosium Integration** (`retail-integration-cosium`)
   - Cosium system entities
   - Tax and client management

6. **PIM Integration** (`retail-integration-pim`)
   - Product information management
   - Product specifications and hierarchies

7. **Database Schemas**
   - SQL table definitions
   - Constraint specifications

### Template Types

#### 1. Core Entity Template
- **Pattern**: `**/Models/**/*Entity.cs`
- **Focus**: Entity Framework entities with full constraint analysis
- **Extracts**: Properties, relationships, validation attributes, inheritance

#### 2. Manage Entity Template
- **Pattern**: `**/Manage/Models/**/*Entity.cs`
- **Focus**: Manage-specific entities with event processing fields
- **Extracts**: Manage-specific fields, event metadata

#### 3. External System Entity Template
- **Pattern**: `**/{Sycle,Simply,Cosium}/**/*Entity.cs`
- **Focus**: System-specific entities with integration fields
- **Extracts**: Integration fields, system-specific mappings

#### 4. Adapter Template
- **Pattern**: `**/*Adapter.cs`
- **Focus**: Data transformation and mapping logic
- **Extracts**: Methods, transformations, validation rules, error handling

#### 5. Configuration Template
- **Pattern**: `**/*Configuration.cs`
- **Focus**: Entity Framework configurations
- **Extracts**: Table mappings, constraints, indexes, fluent API

#### 6. Domain Model Template
- **Pattern**: `**/Models/**/*.cs` (excluding entities, configs, adapters)
- **Focus**: Domain models and DTOs
- **Extracts**: Properties, JSON attributes, validation rules

#### 7. Interface Template
- **Pattern**: `**/I*.cs`
- **Focus**: Interface definitions and contracts
- **Extracts**: Methods, properties, inheritance, generic constraints

#### 8. Context Template
- **Pattern**: `**/*Context.cs`
- **Focus**: Database contexts and configurations
- **Extracts**: DbSets, configurations, schema definitions

#### 9. Database Schema Template
- **Pattern**: `**/*.sql`
- **Focus**: SQL schema definitions
- **Extracts**: Table definitions, constraints, indexes

## 📊 Metadata Extraction

### Enhanced Property Analysis

For each entity property, the system extracts:

- **Data Type Information**: C# type, database type, nullable status
- **Constraints**: Max length, precision, scale, default values
- **Validation Attributes**: Required, Range, RegularExpression, Custom
- **Database Constraints**: Primary keys, foreign keys, check constraints
- **Business Rules**: Custom validation logic and business constraints
- **Relationships**: Navigation properties, foreign key relationships

### Constraint Types Analyzed

1. **Primary Key Constraints**
   - Key definitions and clustering
   - Composite key structures

2. **Foreign Key Constraints**
   - Referenced tables and columns
   - Cascade behaviors (Delete, Update)
   - Referential integrity rules

3. **Unique Constraints**
   - Unique indexes and constraints
   - Composite unique constraints

4. **Check Constraints**
   - Value range validations
   - Business rule enforcement

5. **Not Null Constraints**
   - Required field definitions
   - Nullable property analysis

6. **Default Value Constraints**
   - Default value specifications
   - Computed column definitions

7. **Data Type Constraints**
   - Type-specific limitations
   - Precision and scale for decimals

8. **Validation Attributes**
   - Data annotation validations
   - Custom validator implementations

9. **Business Rules**
   - Cross-property validations
   - Complex business logic constraints

## 🔄 Advanced Usage

### Custom Generation Commands

```bash
# Generate with enhanced constraint analysis
docxf generate --include-constraints

# Generate with cross-system references
docxf generate --include-cross-references

# Generate specific system documentation
docxf generate --source manage_integration
docxf generate --source sycle_integration

# Generate with performance analysis
docxf generate --include-performance

# Generate with validation summary
docxf generate --include-validation-summary
```

### Watch Mode Options

```bash
# Watch with debounce (default 2 seconds)
docxf watch

# Watch with custom debounce
docxf watch --debounce 5000

# Watch specific patterns
docxf watch --pattern "**/*Entity.cs"
docxf watch --pattern "**/*Configuration.cs"
```

## 📈 Output Structure

### Generated Documentation

```
generated/
├── entities/
│   ├── core/                    # Core integration entities
│   ├── manage/                  # Manage system entities
│   ├── sycle/                   # Sycle system entities
│   ├── simply/                  # Simply system entities
│   ├── cosium/                  # Cosium system entities
│   └── pim/                     # PIM system entities
├── adapters/
│   ├── manage/                  # Manage adapters
│   ├── sycle/                   # Sycle adapters
│   └── external/                # External system adapters
├── configurations/
│   ├── entity_configurations/   # EF configurations
│   └── database_schemas/        # SQL schema docs
├── interfaces/
│   ├── core/                    # Core interfaces
│   └── system_specific/         # System-specific interfaces
├── contexts/
│   ├── integration_context/     # Main integration context
│   └── system_contexts/         # System-specific contexts
└── cross_references/
    ├── entity_relationships/    # Cross-entity relationships
    ├── system_mappings/         # Cross-system mappings
    └── constraint_analysis/     # Comprehensive constraint analysis
```

## 🔧 Troubleshooting

### Common Issues

1. **Missing Dependencies**
   ```bash
   npm install -g docxf
   ```

2. **Path Resolution Issues**
   - Ensure all source paths in `docxf.json` are correct
   - Use relative paths from the documentation directory

3. **Template Errors**
   - Check template syntax in `templates/` directory
   - Validate Handlebars expressions

4. **Generation Failures**
   - Check source code compilation
   - Verify file permissions
   - Review docxf logs for specific errors

### Performance Optimization

- Use specific patterns instead of broad wildcards
- Exclude unnecessary directories (bin, obj, packages)
- Adjust debounce timing for watch mode
- Consider generating specific templates for large codebases

## 📝 Customization

### Adding New Templates

1. Create template file in `templates/` directory
2. Add template configuration to `docxf.json`
3. Define metadata extraction rules
4. Test with sample files

### Modifying Existing Templates

1. Edit template files in `templates/` directory
2. Update Handlebars expressions as needed
3. Test generation with modified templates
4. Update documentation as needed

---

*This guide covers the enhanced docxf configuration for comprehensive ERP integration documentation.*
