{"metadata": [{"src": [{"files": ["**/*.c<PERSON><PERSON>j"], "src": "../../integration_solution"}], "dest": "api", "includePrivateMembers": false, "disableGitFeatures": false, "disableDefaultFilter": false, "noRestore": false, "namespaceLayout": "flattened", "memberLayout": "samePage", "allowCompilationErrors": true}], "build": {"content": [{"files": ["api/**.yml", "api/index.md"]}, {"files": ["*.md", "toc.yml"], "exclude": ["**/bin/**", "**/obj/**"]}], "resource": [{"files": ["images/**"]}], "output": "_site", "globalMetadataFiles": [], "fileMetadataFiles": [], "template": ["default", "modern"], "postProcessors": [], "keepFileLink": false, "disableGitFeatures": false, "globalMetadata": {"_appTitle": "Manage-BC Data Transfer Documentation", "_appFooter": "ERP Integration Team - Comprehensive Entity Documentation", "_enableSearch": true, "_enableNewTab": true, "_disableContribution": false, "_gitContribute": {"repo": "https://github.com/your-repo/ERP-Integration", "branch": "main"}}, "fileMetadata": {"_tocTitle": {"api/**.yml": "API Reference"}}}}