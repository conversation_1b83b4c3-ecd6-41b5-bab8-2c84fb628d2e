# 📚 Complete Setup Instructions for Manage-BC Documentation

## 🎯 Overview

This document provides step-by-step instructions to set up comprehensive documentation generation for all entities and their properties with detailed constraints across your ERP integration solution.

## 🚀 Quick Setup

### Step 1: Install DocFX

```bash
# Method 1: Using .NET CLI (Recommended)
dotnet tool install -g docfx

# Method 2: Using Chocolatey (Windows)
choco install docfx

# Method 3: Download from GitHub
# Visit: https://github.com/dotnet/docfx/releases
```

### Step 2: Verify Installation

```bash
# Check DocFX version
docfx --version

# Should output something like: docfx 2.x.x
```

### Step 3: Generate Documentation

```bash
# Navigate to documentation directory
cd docs/Manage_BC

# Option A: Use PowerShell script (Recommended)
.\generate-docs.ps1 -Serve

# Option B: Manual commands
docfx metadata docfx.json
docfx build docfx.json
docfx serve _site
```

## 📊 What Gets Generated

### API Documentation
- **All Entity Classes**: Complete property definitions with constraints
- **Adapter Classes**: Data transformation logic and mappings
- **Configuration Classes**: Entity Framework configurations
- **Interface Definitions**: Contract specifications
- **Database Contexts**: DbContext configurations

### Enhanced Features
- **Cross-References**: Links between related entities
- **Constraint Analysis**: Detailed validation rules and database constraints
- **System Coverage**: All integration systems (Manage, Sycle, Simply, Cosium, PIM)
- **Search Functionality**: Full-text search across all documentation
- **Responsive Design**: Works on desktop and mobile devices

## 🔧 Configuration Files

### `docfx.json`
- Main configuration file for DocFX
- Defines source code locations and build settings
- Configured for all integration projects

### `toc.yml`
- Table of contents structure
- Navigation hierarchy for the documentation site

### `generate-docs.ps1`
- PowerShell script for easy documentation generation
- Includes error handling and status messages
- Supports clean builds and watch mode

## 📁 Output Structure

```
_site/                          # Generated documentation site
├── api/                        # API reference documentation
│   ├── WSA.Retail.Integration.Models.html
│   ├── WSA.Retail.Integration.Manage.html
│   └── ...                     # All namespace documentation
├── index.html                  # Main documentation page
├── toc.html                    # Table of contents
└── ...                         # Supporting files and assets
```

## 🎨 Customization Options

### Templates
The `templates/` directory contains enhanced templates for:
- Entity documentation with constraint analysis
- Adapter documentation with transformation logic
- Configuration documentation with database mappings
- Interface documentation with implementation details

### Styling
- Uses DocFX's modern template
- Customizable CSS and JavaScript
- Responsive design for all devices

## 🔄 Workflow Integration

### Development Workflow
1. **Code Changes**: Make changes to entities, adapters, or configurations
2. **Documentation Update**: Run `.\generate-docs.ps1` to regenerate docs
3. **Review**: Check the generated documentation at `http://localhost:8080`
4. **Commit**: Include updated documentation in your commits

### Continuous Integration
Add to your CI/CD pipeline:
```yaml
- name: Generate Documentation
  run: |
    cd docs/Manage_BC
    docfx metadata docfx.json
    docfx build docfx.json
```

## 🔍 Advanced Usage

### Watch Mode
```bash
# Automatically rebuild when files change
.\generate-docs.ps1 -Watch
```

### Clean Build
```bash
# Clean previous build and regenerate
.\generate-docs.ps1 -Clean -Serve
```

### API-Only Generation
```bash
# Generate only API documentation
docfx metadata docfx.json
```

## 🛠️ Troubleshooting

### Common Issues

1. **DocFX Not Found**
   ```bash
   # Ensure .NET SDK is installed
   dotnet --version
   
   # Reinstall DocFX
   dotnet tool uninstall -g docfx
   dotnet tool install -g docfx
   ```

2. **Build Errors**
   - Check that all referenced projects can compile
   - Ensure all NuGet packages are restored
   - Review error messages in the console output

3. **Missing Documentation**
   - Verify XML documentation is enabled in project files
   - Check that source paths in `docfx.json` are correct
   - Ensure projects build successfully

### Performance Tips
- Use `allowCompilationErrors: true` for partial builds
- Exclude test projects if not needed
- Consider splitting large solutions into multiple documentation sites

## 📝 Maintenance

### Regular Updates
- Update DocFX periodically: `dotnet tool update -g docfx`
- Review and update templates as needed
- Keep documentation in sync with code changes

### Quality Checks
- Regularly review generated documentation for accuracy
- Ensure all entities have proper XML documentation comments
- Validate that constraint information is complete and accurate

## 🎉 Success!

Once set up, you'll have:
- ✅ Comprehensive entity documentation with detailed constraints
- ✅ Automatic API reference generation from source code
- ✅ Cross-system relationship documentation
- ✅ Search functionality across all documentation
- ✅ Professional, responsive documentation site
- ✅ Easy maintenance and updates

Your documentation will automatically reflect changes in your source code, providing always up-to-date information about entities, their properties, constraints, and relationships across all integrated systems.

---

*For additional help, refer to the [DocFX documentation](https://dotnet.github.io/docfx/) or the `DOCXF_GUIDE.md` file.*
